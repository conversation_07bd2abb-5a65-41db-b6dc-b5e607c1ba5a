{% set gs = craft.app.globals.getSetByHandle('footerBlocks') %}

{% include "_pagebuilder/render" with {
  blocks: gs.contentBlocks.all() ?? []
} %}

<footer class="relative">
	<svg viewbox="0 0 1728 103" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-full text-background-muted">
		<path d="M0 0H1728C1728 0 1498.5 103 864 103C229.5 103 0 0 0 0Z" fill="currentColor"/>
	</svg>
	<div class="container py-24">
		<div class="grid grid-cols-2">
			<div>
				<img src="/assets/images/hm_logo.png" alt="Helga Mahrer Logo" class="h-10"/>
			</div>
			<div class="grid grid-cols-2">
				<ul class="space-y-4">
					<li>
						<a class="hover:underline font-medium" href="{{ siteUrl }}">
							<span>Home</span>
						</a>
					</li>
					<li>
						<a class="hover:underline font-medium" href="{{ siteUrl }}">
							<span>Persönlichkeit trifft Körper</span>
						</a>
					</li>
					<li>
						<a class="hover:underline font-medium" href="{{ siteUrl }}">
							<span>Persönlichkeit trifft Essen</span>
						</a>
					</li>
					<li>
						<a class="hover:underline font-medium" href="{{ siteUrl }}">
							<span>Über mich</span>
						</a>
					</li>
					<li>
						<a class="hover:underline font-medium" href="{{ siteUrl }}">
							<span>Podcast</span>
						</a>
					</li>
					<li>
						<a class="hover:underline font-medium" href="{{ siteUrl }}">
							<span>Kontakt</span>
						</a>
					</li>
				</ul>
				<ul class="space-y-4">
					<li>
						<a class="hover:underline font-medium" href="{{ siteUrl }}">
							<span>Impressum</span>
						</a>
					</li>
					<li>
						<a class="hover:underline font-medium" href="{{ siteUrl }}">
							<span>Datenschutz</span>
						</a>
					</li>
					<li>
						<a class="hover:underline font-medium" href="{{ siteUrl }}">
							<span>Cookie Einstellungen</span>
						</a>
					</li>
				</ul>
			</div>
		</div>
	</div>
</footer>
