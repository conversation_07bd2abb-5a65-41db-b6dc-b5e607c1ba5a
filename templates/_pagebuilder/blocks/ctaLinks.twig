{% import "_macros/image.twig" as m %}

<section class="relative">
	<div class="container grid grid-cols-2 gap-5">
		{% for link in mb.links %}
			<div class="py-52">
				<div class="relative flex justify-center overflow-hidden z-0">
					{{ m.img(link.images[0], {
                        width: 1000,
                        transform: 'crop',
                        class: 'h-[435px] w-[330px] object-cover rounded-3xl relative z-10'
                    }) }}
					{{ m.img(link.images[1], {
                        width: 1000,
                        transform: 'crop',
                        class: 'h-[370px] w-[280px] object-cover rounded-3xl absolute bottom-0 left-1/2 rotate-10'
                    }) }}
					{{ m.img(link.images[2], {
                        width: 1000,
                        transform: 'crop',
                        class: 'h-[370px] w-[280px] object-cover rounded-3xl absolute -bottom-1/8 right-1/2 -rotate-10'
                    }) }}
					<div class="absolute inset-0 z-20">
						<div class="progressive-blur-container">
							<div class="blur-filter"></div>
							<div class="blur-filter"></div>
							<div class="blur-filter"></div>
							<div class="blur-filter"></div>
							<div class="blur-filter"></div>
							<div class="blur-filter"></div>
							<div class="blur-filter"></div>
							<div class="bg-gradient-to-b from-transparent to-background absolute inset-0"></div>
						</div>
					</div>
				</div>
				<div class="flex flex-col items-center gap-4 text-center relative z-10 -mt-24">
					<h3>{{ link.title }}</h3>
					<h4 class="text-foreground-muted">{{ link.subtitle }}</h4>
					<a href="{{ link.buttonUrl }}" class="mt-12 btn {{ link.theme }}">{{ link.buttonText }}</a>
				</div>
			</div>
		{% endfor %}
	</div>
</section>
