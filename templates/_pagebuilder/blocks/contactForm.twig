<section class="relative">
	<div class="container grid grid-cols-2 gap-5 py-52">
		<div class="prose | text-balance">
			{{ mb.titleRichText }}
			{{ mb.body }}
		</div>

		<div class="flex justify-center">
			{% macro errorList(errors) %}
				{% if errors %}
					<ul class="mt-2 text-sm text-accent-food space-y-1">
						{% for error in errors %}
							<li>{{ error }}</li>
						{% endfor %}
					</ul>
				{% endif %}
			{% endmacro %}

			{% from _self import errorList %}

			{% set form = wheelform.form({
                    id: mb.form.id, 
                    redirect: 'kontakt/danke',
                    attributes: {
                        id: 'contact-form',
                        class: 'space-y-6 max-w-[480px] w-full'
                    },
                    submitButton: {
                        label: "Nachricht senden",
                        type: 'button',
                        attributes: {
                            class: 'btn muted'
                        }
                    }
            }) %}

			{{ form.open() }}

			{% for field in form.fields %}
				{% switch field.type %}
					{% case "checkbox" %}
						<div class="form-checkbox">
							{% for item in field.items %}
								<label>
									<input class="checkbox" type="checkbox" value="{{ item }}" {{values[field.name] is defined and item in values[field.name] ? ' checked="checked"' : '' }} name="{{field.name}}[]" id=""/>
									{{field.label}}
								</label>
							{% endfor %}
						</div>
					{% case "radio" %}
						<div class="form-radio">
							{% for item in field.items %}
								<input class="radio" type="radio" value="{{ item }}" {{values[field.name] is defined and item == values[field.name] ? ' checked="checked"' : '' }} name="{{field.name}}" id=""/>
								<label>{{item}}</label>
							{% endfor %}
						</div>
					{% case "select" %}
						<div class="form-select">
							<select id="wf-select" name="{{field.name}}" class="wf-field {{field.fieldClass}}">
								{% for item in field.items %}
									<option value="{{ item }}" {{values[field.name] is defined and item == values[field.name] ? 'selected="selected"' : '' }}>{{item}}</option>
								{% endfor %}
							</select>
						</div>
					{% case "file" %}
						<div class="form-group">
							<label>{{field.label}}</label>
							<input type="file" name="{{field.name}}" id=""/>
						</div>
					{% case "textarea" %}
						<div class="form-group w-full">
							<textarea class="form-control | w-full py-3 border-b border-foreground focus:outline-none" placeholder="{{field.placeholder}}" rows="5" name="{{field.name}}" id="">{{ values[field.name] ?? '' }}</textarea>
						</div>
					{% case "list" %}
						<div class="form-group">
							<label>{{field.label}}</label>
							<input type="text" name="{{field.name}}[]" id=""/>
						</div>
					{% default %}
						<div class="form-group w-full">
							<input class="form-control | w-full py-3 border-b border-foreground focus:outline-none" type="{{field.type}}" value="{{ values[field.name] ?? '' }}" name="{{field.name}}" placeholder="{{field.placeholder}}" id=""/>
						</div>
				{% endswitch %}
				{# {{ wheelformErrors[field.name] is defined ? errorList(wheelformErrors[field.name]) }} #}
			{% endfor %}

			{{ form.close() }}
		</div>
	</div>
</section>
