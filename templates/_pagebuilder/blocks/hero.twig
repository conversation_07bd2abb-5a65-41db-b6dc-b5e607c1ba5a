{% import "_macros/image.twig" as m %}
{% set img = mb.backgroundImage.one() %}

<section class="relative bg-background-muted min-h-[80svh]">
	<div class="container flex items-center min-h-[80svh]">
		<div class="space-y-6 max-w-lg relative z-10">
			<h1>{{ mb.title }}</h1>
			<h3 class="text-foreground-muted">{{ mb.subtitle }}</h3>
			{% if mb.buttonText %}
				<a href="{{ mb.buttonUrl }}" class="btn mt-12">{{ mb.buttonText }}</a>
			{% endif %}
		</div>
	</div>
	{% if img %}
		<div class="absolute inset-0 flex items-end justify-end">
			{{ m.img(img, {
                width: 2880,
                transform: 'crop',
                class: 'h-7/8 w-4/5 object-contain'
            }) }}
		</div>
	{% endif %}
</section>
