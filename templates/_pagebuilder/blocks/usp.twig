{% import "_macros/image.twig" as m %}
{% set itemsLength = mb.usp_items|length %}

<section class="relative min-h-screen" data-section="usp">
	<div class="container grid grid-cols-2 gap-5 h-screen justify-center">
		<div class="h-full relative prose">
			{% for item in mb.usp_items %}
				<div data-text="{{ loop.index }}" class="h3 | absolute top-1/2 left-0 -translate-y-1/2" style="z-index: {{ itemsLength - loop.index }}">
					{{ item.body }}
				</div>
			{% endfor %}
		</div>
		<div class="h-full relative">
			{% for item in mb.usp_items %}
				<div data-image="{{ loop.index }}" class="absolute top-1/2 left-0 -translate-y-1/2" style="z-index: {{ itemsLength - loop.index }}; transform: translateX({{ (loop.index) * 10 }}%) scale({{ 1 - (loop.index * 0.1) }});">
					<div class="overflow-hidden rounded-4xl relative h-[620px] w-[470px]">
						<img src="{{ item.image.one().url }}" class="h-full w-full object-cover"/>
					</div>
				</div>
			{% endfor %}
		</div>
	</div>
</section>
