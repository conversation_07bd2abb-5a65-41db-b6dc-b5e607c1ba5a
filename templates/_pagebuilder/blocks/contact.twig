<section class="relative bg-background-muted">
	<div class="container py-52 space-y-24">
		<div class="max-w-2xl space-y-24">
			<div class="prose | text-balance">
				{{ mb.titleRichText }}
				{{ mb.body }}
			</div>
			<input class="form-control | max-w-[480px] w-full py-3 border-b border-foreground focus:outline-none" name="newsletter_email" placeholder="Email" id="newsletter_email"/>
		</div>
		<div class="grid grid-cols-3 gap-5">
			{% for office in mb.offices %}
				<div class="col-span-1 space-y-8">
					<h4>{{ office.title }}</h4>
					<div class="prose">
						{{ office.body }}
					</div>
					{% if office.buttonUrl %}
						<a href="{{ office.buttonUrl }}" class="btn">{{ office.buttonText }}</a>
					{% endif %}
				</div>
			{% endfor %}
			<div class="col-span-1 space-y-8">
				<h4><PERSON><PERSON><PERSON><PERSON><PERSON></h4>
				<div class="space-y-4">
					<div class="flex items-center gap-x-2">
						<svg
							xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewbox="0 0 24 24">
							<!-- Icon from Material Symbols by Google - https://github.com/google/material-design-icons/blob/master/LICENSE -->
							<path fill="currentColor" d="M12 22q-2.075 0-3.9-.788t-3.175-2.137T2.788 15.9T2 12t.788-3.9t2.137-3.175T8.1 2.788T12 2t3.9.788t3.175 2.137T21.213 8.1T22 12v1.45q0 1.475-1.012 2.513T18.5 17q-.875 0-1.65-.375t-1.3-1.075q-.725.725-1.638 1.088T12 17q-2.075 0-3.537-1.463T7 12t1.463-3.537T12 7t3.538 1.463T17 12v1.45q0 .65.425 1.1T18.5 15t1.075-.45t.425-1.1V12q0-3.35-2.325-5.675T12 4T6.325 6.325T4 12t2.325 5.675T12 20h5v2zm0-7q1.25 0 2.125-.875T15 12t-.875-2.125T12 9t-2.125.875T9 12t.875 2.125T12 15"/>
						</svg>
						<a href="mailto:{{ mb.email }}" class="hover:underline">
							{{ mb.email }}
						</a>
					</div>
					<div class="flex items-center gap-x-2">
						<svg
							xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewbox="0 0 24 24">
							<!-- Icon from Material Symbols by Google - https://github.com/google/material-design-icons/blob/master/LICENSE -->
							<path fill="currentColor" d="M19.95 21q-3.125 0-6.187-1.35T8.2 15.8t-3.85-5.55T3 4.05V3h5.9l.925 5.025l-2.85 2.875q.55.975 1.225 1.85t1.45 1.625q.725.725 1.588 1.388T13.1 17l2.9-2.9l5 1.025V21z"/>
						</svg>
						<a href="tel:{{ mb.phoneNumber }}" class="hover:underline">
							{{ mb.phoneNumber }}
						</a>
					</div>
				</div>
			</div>
		</div>
	</div>
</section>
