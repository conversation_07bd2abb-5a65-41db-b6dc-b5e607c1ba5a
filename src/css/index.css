@import 'tailwindcss';

@source '../../';
/* 
html, body {
  position: relative;
  overflow-x: hidden;
} */

:root {
    --color-foreground: #3B3D39;
    --color-foreground-muted: #828282;

    --color-background: #FFF;
    --color-background-muted: #F4F4F4;

    --color-accent-body: #00AA1B;
    --color-accent-food: #F43F5E;
}

@theme {
    --color-foreground: var(--color-foreground);
    --color-foreground-muted: var(--color-foreground-muted);

    --color-background: var(--color-background);
    --color-background-muted: var(--color-background-muted);

    --color-accent-body: var(--color-accent-body);
    --color-accent-food: var(--color-accent-food);
}

body {
  color: var(--color-foreground);
  background: var(--color-background);
}

@utility container {
  padding-inline: 1rem;
  margin: 0 auto;
}

.btn {
  background: var(--color-background);
  padding: 12px 12px 12px 24px;
  border-radius: 9999px;
  font-size: 16px;
  font-weight: 600;
  color: var(--color-foreground);
  display: inline-flex;
  align-items: center;
  gap: 20px;

  &::after {
    content: "";
    height: 32px;
    width: 32px;
    background-color: var(--color-accent-body);
    border-radius: 50%;
    display: inline-block;

    background-image: url("data:image/svg+xml;utf8,\<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23fff' stroke-width='2.5' stroke-linecap='round' stroke-linejoin='round'>\<path d='M7 17L17 7'/>\<path d='M9 7h8v8'/>\</svg>");
    background-repeat: no-repeat;
    background-position: center;
    background-size: 60%;
  }

  &.muted {
    background-color: var(--color-background-muted);
  }

  &.body {
    @apply bg-accent-body/10;
    &::after { @apply bg-accent-body; }
  }

  &.food {
    @apply bg-accent-food/10;
    &::after { @apply bg-accent-food; }
  }
}

h1, .h1 {
  font-size: 72px;
  line-height: 69px;
  letter-spacing: -3.6px;
  font-weight: 600;
}

h2, .h2 {
  font-size: 72px;
  line-height: 69px;
  letter-spacing: -3.6px;
  font-weight: 600;
}

h3, .h3, .h3 > p {
  font-size: 34px;
  line-height: 1;
  letter-spacing: -1.7px;
  font-weight: 600;
}

h4, .h4 {
  font-size: 24px;
  line-height: 1;
  letter-spacing: -0.72px;
  font-weight: 600;
}

p, .p {
  font-size: 18px;
  line-height: 1.5;
}

.progressive-blur-container {
  position: absolute;
  left: 0;
  bottom: 0;
  right: 0;
  height: 50%;
  width: 100%;
  pointer-event: none;
}

.progressive-blur-container > .blur-filter {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
}

.progressive-blur-container > .blur-filter:nth-child(1) {
  backdrop-filter: blur(1px);
  mask: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0),
    rgba(0, 0, 0, 1) 10%,
    rgba(0, 0, 0, 1) 30%,
    rgba(0, 0, 0, 0) 40%
  );
}

.progressive-blur-container > .blur-filter:nth-child(2) {
  backdrop-filter: blur(2px);
  mask: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0) 10%,
    rgba(0, 0, 0, 1) 20%,
    rgba(0, 0, 0, 1) 40%,
    rgba(0, 0, 0, 0) 50%
  );
}

.progressive-blur-container > .blur-filter:nth-child(3) {
  backdrop-filter: blur(4px);
  mask: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0) 15%,
    rgba(0, 0, 0, 1) 30%,
    rgba(0, 0, 0, 1) 50%,
    rgba(0, 0, 0, 0) 60%
  );
}

.progressive-blur-container > .blur-filter:nth-child(4) {
  backdrop-filter: blur(8px);
  mask: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0) 20%,
    rgba(0, 0, 0, 1) 40%,
    rgba(0, 0, 0, 1) 60%,
    rgba(0, 0, 0, 0) 70%
  );
}

.progressive-blur-container > .blur-filter:nth-child(5) {
  backdrop-filter: blur(16px);
  mask: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0) 40%,
    rgba(0, 0, 0, 1) 60%,
    rgba(0, 0, 0, 1) 80%,
    rgba(0, 0, 0, 0) 90%
  );
}

.progressive-blur-container > .blur-filter:nth-child(6) {
  backdrop-filter: blur(32px);
  mask: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0) 60%,
    rgba(0, 0, 0, 1) 80%
  );
}

.progressive-blur-container > .blur-filter:nth-child(7) {
  z-index: 10;
  background-filter: blur(64px);
  mask: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0) 70%,
    rgba(0, 0, 0, 1) 100%
  );
}

.prose h2, .prose .h2, .prose h3, .prose .h3 {
  color: var(--color-foreground-muted);
  margin-bottom: 48px;
}

.prose h2 i, .prose .h2 i, .prose h3 i, .prose .h3 i,
.prose h2 strong, .prose .h2 strong, .prose h3 strong, .prose .h3 strong {
  color: var(--color-foreground);
  font-style: normal;
}

.prose p {
  color: var(--color-foreground-muted);
}