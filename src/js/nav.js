document.addEventListener("DOMContentLoaded", () => {
	// ====== Desktop header hide/reveal logic ======
	const header = document.querySelector('[data-header="desktop"]');
	if (!header) return;

	const THRESHOLD = 100; // px from top
	const mqDesktop = window.matchMedia("(min-width: 1024px)"); // desktop breakpoint
	const mqMobile = window.matchMedia("(max-width: 1023.98px)");

	let desktopEnabled = mqDesktop.matches;
	let lastY = window.scrollY; // used only for desktop hide/reveal
	let ticking = false;

	// ====== Mobile menu elements ======
	const openBtn = document.querySelector("[data-mobile-open]");
	const closeBtn = document.querySelector("[data-mobile-close]");
	const overlay = document.querySelector("[data-mobile-overlay]");
	const backdrop = document.querySelector("[data-mobile-backdrop]");
	const linkButtons = Array.from(
		document.querySelectorAll("[data-mobile-link]"),
	);

	let menuOpen = false;

	// Body scroll lock helpers
	const lockScroll = () => {
		const scrollBarComp =
			window.innerWidth - document.documentElement.clientWidth;
		document.body.style.overflow = "hidden";
		if (scrollBarComp > 0)
			document.body.style.paddingRight = scrollBarComp + "px";
		document.documentElement.classList.add("is-menu-open");
	};
	const unlockScroll = () => {
		document.body.style.overflow = "";
		document.body.style.paddingRight = "";
		document.documentElement.classList.remove("is-menu-open");
	};

	const setMenuState = (open) => {
		menuOpen = !!open;
		if (overlay) overlay.classList.toggle("is-open", menuOpen);
		if (openBtn) openBtn.setAttribute("aria-expanded", String(menuOpen));
		if (menuOpen) lockScroll();
		else unlockScroll();

		// ensure header is visible and solid when menu opens
		if (menuOpen) {
			header.classList.remove("is-hidden", "is-transparent");
			header.classList.add("is-solid");
		}
	};

	// ====== Header background (all breakpoints) ======
	// Only toggles solid/transparent. No hiding here.
	const applyHeaderBackground = () => {
		// keep solid while menu is open
		if (menuOpen) {
			header.classList.remove("is-transparent");
			header.classList.add("is-solid");
			return;
		}

		const y = window.scrollY;
		if (y <= THRESHOLD) {
			header.classList.remove("is-solid");
			header.classList.add("is-transparent");
		} else {
			header.classList.remove("is-transparent");
			header.classList.add("is-solid");
		}
	};

	// ====== Desktop hide/reveal (desktop only) ======
	const applyDesktopHideReveal = () => {
		if (!desktopEnabled) {
			header.classList.remove("is-hidden"); // never hidden on mobile/tablet
			return;
		}
		if (menuOpen) {
			// while menu open, keep visible + solid
			header.classList.remove("is-hidden", "is-transparent");
			header.classList.add("is-solid");
			return;
		}

		const y = window.scrollY;
		const scrollingDown = y > lastY;

		if (y <= THRESHOLD) {
			header.classList.remove("is-hidden", "is-solid");
			header.classList.add("is-transparent");
		} else {
			if (scrollingDown) {
				header.classList.add("is-hidden");
			} else {
				header.classList.remove("is-hidden", "is-transparent");
				header.classList.add("is-solid");
			}
		}
		lastY = y;
	};

	// ====== Combined RAF tick ======
	const tick = () => {
		applyHeaderBackground(); // always (mobile + desktop)
		applyDesktopHideReveal(); // only matters on desktop
		ticking = false;
	};

	const onScroll = () => {
		if (!ticking) {
			ticking = true;
			requestAnimationFrame(tick);
		}
	};

	const onDesktopMQChange = () => {
		desktopEnabled = mqDesktop.matches;
		if (!desktopEnabled) header.classList.remove("is-hidden");
		// re-evaluate background immediately for new breakpoint
		applyHeaderBackground();
		applyDesktopHideReveal();
	};

	// ====== Mobile menu events ======
	const onOpenClick = (e) => {
		e.preventDefault();
		if (!mqMobile.matches) return;
		setMenuState(true);
		const firstFocusable =
			overlay &&
			overlay.querySelector(
				'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])',
			);
		if (firstFocusable) firstFocusable.focus();
	};
	const onCloseClick = (e) => {
		e.preventDefault();
		if (!mqMobile.matches) return;
		setMenuState(false);
		if (openBtn) openBtn.focus();
	};
	const onBackdropClick = (e) => {
		if (!mqMobile.matches) return;
		if (e.target === backdrop) setMenuState(false);
	};
	const onEsc = (e) => {
		if (!mqMobile.matches) return;
		if (e.key === "Escape" && menuOpen) {
			setMenuState(false);
			if (openBtn) openBtn.focus();
		}
	};
	const onLinkClick = () => {
		if (!mqMobile.matches) return;
		setMenuState(false);
	};

	// Simple focus trap while open
	const onKeydownTrap = (e) => {
		if (!menuOpen || !mqMobile.matches || !overlay) return;
		if (e.key !== "Tab") return;
		const focusables = overlay.querySelectorAll(
			'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])',
		);
		const list = Array.from(focusables).filter(
			(el) => !el.hasAttribute("disabled"),
		);
		if (list.length === 0) return;
		const first = list[0],
			last = list[list.length - 1];
		if (e.shiftKey && document.activeElement === first) {
			last.focus();
			e.preventDefault();
		} else if (!e.shiftKey && document.activeElement === last) {
			first.focus();
			e.preventDefault();
		}
	};

	// Wire up mobile controls
	if (openBtn) openBtn.addEventListener("click", onOpenClick);
	if (closeBtn) closeBtn.addEventListener("click", onCloseClick);
	if (backdrop) backdrop.addEventListener("click", onBackdropClick);
	linkButtons.forEach((a) => a.addEventListener("click", onLinkClick));
	document.addEventListener("keydown", onEsc);
	document.addEventListener("keydown", onKeydownTrap);

	// Scroll + resize listeners
	window.addEventListener("scroll", onScroll, { passive: true });
	if (mqDesktop.addEventListener)
		mqDesktop.addEventListener("change", onDesktopMQChange);
	else mqDesktop.addListener(onDesktopMQChange);

	const onMobileMQChange = () => {
		if (!mqMobile.matches && menuOpen) setMenuState(false);
	};
	if (mqMobile.addEventListener)
		mqMobile.addEventListener("change", onMobileMQChange);
	else mqMobile.addListener(onMobileMQChange);

	// initial paint
	applyHeaderBackground();
	applyDesktopHideReveal();
});
