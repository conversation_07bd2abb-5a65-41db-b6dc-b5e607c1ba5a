columnSuffix: null
handle: service_prices
instructions: null
name: Prices
searchable: false
settings:
  createButtonLabel: null
  defaultIndexViewMode: cards
  enableVersioning: false
  entryTypes:
    -
      __assoc__:
        -
          - uid
          - 4752da2c-30be-4fc7-b165-a64eb5cbe77c # Default Prices
        -
          - group
          - General
  includeTableView: false
  maxEntries: null
  minEntries: null
  pageSize: 50
  propagationKeyFormat: null
  propagationMethod: all
  showCardsInGrid: false
  viewMode: cards
translationKeyFormat: null
translationMethod: site
type: craft\fields\Matrix
