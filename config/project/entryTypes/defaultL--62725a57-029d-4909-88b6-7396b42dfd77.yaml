color: null
description: null
fieldLayouts:
  62ef168f-b375-4c22-99ef-3fe7920162b2:
    cardThumbAlignment: end
    tabs:
      -
        elementCondition: null
        elements:
          -
            autocapitalize: true
            autocomplete: false
            autocorrect: true
            class: null
            dateAdded: '2025-10-02T08:26:10+00:00'
            disabled: false
            elementCondition: null
            id: null
            includeInCards: false
            inputType: null
            instructions: null
            label: null
            max: null
            min: null
            name: null
            orientation: null
            placeholder: null
            providesThumbs: false
            readonly: false
            required: true
            size: null
            step: null
            tip: null
            title: null
            type: craft\fieldlayoutelements\entries\EntryTitleField
            uid: b2018ec6-e5ff-4f78-99e9-1a1a7f590c85
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-10-02T08:28:06+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: d62a9951-5dc6-4ad2-9701-6e3d8909e8e9 # Subtitle
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 9ce3036e-e3c8-459c-aa60-b10303097cbe
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-10-02T08:31:58+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: eabc9ceb-dc46-451e-b1de-98934d666e25 # Theme
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: fc49f923-5851-4e31-af40-25f821856420
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-10-02T08:28:06+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 7348bf73-ea1c-42c4-b6c3-2489ecaaa66a # Images
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: b7d54ed3-db78-464a-8dd2-93b4314f0ace
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-10-02T08:28:06+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 550d8241-819c-42f5-bcf8-47fd9f6016fc # Button Text
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 2d68b70c-d509-4ee6-a542-adc53f5d40e0
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-10-02T08:28:06+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 6a337d87-a9f1-46b1-82f8-9fe1e207b016 # ButtonUrl
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: b1bad35c-cbe5-4dc5-a230-42f99f93c359
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: 594698cd-4c3d-40d6-b591-50cf3b0b24f3
        userCondition: null
handle: defaultL
hasTitleField: true
icon: null
name: default_L
showSlugField: true
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: null
titleTranslationKeyFormat: null
titleTranslationMethod: site
