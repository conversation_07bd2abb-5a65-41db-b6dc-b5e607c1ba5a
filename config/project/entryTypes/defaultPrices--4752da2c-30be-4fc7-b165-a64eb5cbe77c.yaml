color: null
description: null
fieldLayouts:
  85a25718-562a-4e19-a344-2698476a0b2a:
    cardThumbAlignment: end
    tabs:
      -
        elementCondition: null
        elements:
          -
            autocapitalize: true
            autocomplete: false
            autocorrect: true
            class: null
            dateAdded: '2025-09-02T08:01:08+00:00'
            disabled: false
            elementCondition: null
            id: null
            includeInCards: false
            inputType: null
            instructions: null
            label: null
            max: null
            min: null
            name: null
            orientation: null
            placeholder: null
            providesThumbs: false
            readonly: false
            required: true
            size: null
            step: null
            tip: null
            title: null
            type: craft\fieldlayoutelements\entries\EntryTitleField
            uid: f25fde5a-a473-4f53-ba0d-3e6c34438b83
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-09-02T08:02:58+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: fcee3d3e-fac9-4517-9cc9-dd03bb41b028 # Price
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 62ff7c07-3671-4f06-b4ae-f9b5d699abea
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: a9408e7c-6e9b-4a1f-9b70-bb186a978a0a
        userCondition: null
handle: defaultPrices
hasTitleField: true
icon: null
name: 'Default Prices'
showSlugField: true
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: null
titleTranslationKeyFormat: null
titleTranslationMethod: site
