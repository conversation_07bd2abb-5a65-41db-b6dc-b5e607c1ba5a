color: null
description: null
fieldLayouts:
  b947a5cb-d015-43e3-b7d1-6bafa2bc8511:
    cardThumbAlignment: end
    tabs:
      -
        elementCondition: null
        elements:
          -
            autocapitalize: true
            autocomplete: false
            autocorrect: true
            class: null
            dateAdded: '2025-09-02T07:19:51+00:00'
            disabled: false
            elementCondition: null
            id: null
            includeInCards: false
            inputType: null
            instructions: null
            label: null
            max: null
            min: null
            name: null
            orientation: null
            placeholder: null
            providesThumbs: false
            readonly: false
            required: true
            size: null
            step: null
            tip: null
            title: null
            type: craft\fieldlayoutelements\entries\EntryTitleField
            uid: 7473b1b5-64fa-4ead-a741-258adc56babc
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-09-02T07:23:31+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 43d6ef5a-476a-4272-8468-cffb2d937bf2 # Testimonials
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: d2852541-e68b-4f95-ba87-a45c734752b4
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-09-02T08:03:22+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 1bc0f216-114d-4d0e-adcc-fb6b648814c8 # Prices
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: e7f50a66-e682-4fb6-b3bf-b0a3e10263c8
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: 0666b0a9-738b-40c6-ab11-7c2a7fe09a3d
        userCondition: null
handle: testimonialsPricelist
hasTitleField: true
icon: null
name: 'Testimonials & Pricelist'
showSlugField: true
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: null
titleTranslationKeyFormat: null
titleTranslationMethod: site
