color: null
description: null
fieldLayouts:
  be9cf4cf-62cc-4718-b366-08b29148ad0a:
    cardThumbAlignment: end
    tabs:
      -
        elementCondition: null
        elements:
          -
            autocapitalize: true
            autocomplete: false
            autocorrect: true
            class: null
            dateAdded: '2025-10-02T11:48:53+00:00'
            disabled: false
            elementCondition: null
            id: null
            includeInCards: false
            inputType: null
            instructions: null
            label: null
            max: null
            min: null
            name: null
            orientation: null
            placeholder: null
            providesThumbs: false
            readonly: false
            required: true
            size: null
            step: null
            tip: null
            title: null
            type: craft\fieldlayoutelements\entries\EntryTitleField
            uid: 09270849-3713-4f9f-8e2f-8d26681adbe1
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-10-02T11:50:30+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 77ef78e1-9d09-495d-b223-c9e4f0fb6c7e # Body
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 106999f3-e681-46af-8221-9a4e300dec65
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-10-02T11:50:30+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 550d8241-819c-42f5-bcf8-47fd9f6016fc # Button Text
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: d9cadae4-bd39-4972-ac35-361ca3f14556
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-10-02T11:50:30+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 6a337d87-a9f1-46b1-82f8-9fe1e207b016 # ButtonUrl
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 2f0b24a0-727a-43fb-acfe-b9e4453e0300
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: 89d35cce-8762-4d0c-9377-0db80ff871d1
        userCondition: null
handle: officesdefault
hasTitleField: true
icon: null
name: officesDefault
showSlugField: true
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: null
titleTranslationKeyFormat: null
titleTranslationMethod: site
