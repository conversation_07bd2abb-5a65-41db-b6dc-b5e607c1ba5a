color: null
description: null
fieldLayouts:
  bef621bb-c1b7-4005-a41a-8c2d30888781:
    cardThumbAlignment: end
    cardView:
      - 'layoutElement:0dca0c8d-1888-473b-b10b-dbefff341368'
    tabs:
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2025-09-02T07:23:23+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 77ef78e1-9d09-495d-b223-c9e4f0fb6c7e # Body
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 66d2fe44-f360-4f3d-bac8-b8e22d45f93e
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-09-02T07:23:23+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: dcca89a7-a5da-4004-89dc-bdeacac2d4be # Author
            handle: writer
            includeInCards: true
            instructions: null
            label: Writer
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 0dca0c8d-1888-473b-b10b-dbefff341368
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: 33e91e86-c30f-4217-a8f0-1ffcee14c194
        userCondition: null
handle: defaultTestimonials
hasTitleField: false
icon: null
name: 'Default Testimonials'
showSlugField: true
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: null
titleTranslationKeyFormat: null
titleTranslationMethod: site
