color: null
description: null
fieldLayouts:
  5efa3b9c-64ad-4c96-848c-333dab9b4fc0:
    cardThumbAlignment: end
    tabs:
      -
        elementCondition: null
        elements:
          -
            dateAdded: '2025-09-01T11:00:56+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: e2608f55-b09a-44e7-a593-42ba1a9932f8 # Image
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 391a9ac3-a0f8-45ed-94da-07f5d1c12097
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-09-01T11:00:56+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 77ef78e1-9d09-495d-b223-c9e4f0fb6c7e # Body
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 5567bbd9-a0e3-49fa-9605-af970594eb96
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-09-01T11:00:56+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 49df8875-811b-4d18-8b70-f387e1047bdb # Image Position
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 907dd351-fca4-4e49-ac45-f58146a4aa4f
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: 110c6cf7-07f4-472b-88b4-d9dd6b276f51
        userCondition: null
handle: imageWithText
hasTitleField: false
icon: null
name: 'Image With Text'
showSlugField: true
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: null
titleTranslationKeyFormat: null
titleTranslationMethod: site
