color: null
description: null
fieldLayouts:
  7865bcb3-a7cf-4370-9121-972f627bc74d:
    cardThumbAlignment: end
    tabs:
      -
        elementCondition: null
        elements:
          -
            autocapitalize: true
            autocomplete: false
            autocorrect: true
            class: null
            dateAdded: '2025-09-01T10:46:28+00:00'
            disabled: false
            elementCondition: null
            id: null
            includeInCards: false
            inputType: null
            instructions: null
            label: null
            max: null
            min: null
            name: null
            orientation: null
            placeholder: null
            providesThumbs: false
            readonly: false
            required: true
            size: null
            step: null
            tip: null
            title: null
            type: craft\fieldlayoutelements\entries\EntryTitleField
            uid: 2b40cfe9-afd9-47d3-80d6-52fff2e7764f
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-09-01T11:03:08+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 836f53cd-ec58-4d3a-a121-266d5d5f2979 # Content Blocks
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 364eb815-b6ef-4a21-be03-f25e0eee7d3a
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: 07c3e7a1-3959-4ab9-af6d-0934fee31f86
        userCondition: null
handle: default
hasTitleField: true
icon: null
name: Default
showSlugField: true
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: null
titleTranslationKeyFormat: null
titleTranslationMethod: site
